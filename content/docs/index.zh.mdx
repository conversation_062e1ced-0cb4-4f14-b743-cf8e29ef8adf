---
title: 文档系统
description: 基于 Fumadocs 的现代化文档系统
---

## 欢迎使用文档系统

这是一个基于 **Fumadocs** 构建的现代化文档系统，集成了 Next.js 15、React 19 和 TypeScript。

### 🚀 主要特性

- **现代化 UI**: 使用 Fumadocs UI 构建的美观、响应式设计
- **MDX 支持**: 在 Markdown 中使用 React 组件
- **代码高亮**: 支持多种编程语言的语法高亮
- **搜索功能**: 内置搜索功能
- **深色模式**: 自动深色/浅色模式支持
- **类型安全**: 完整的 TypeScript 支持
- **国际化**: 支持多语言切换

### 📁 项目结构

```
├── content/docs/          # 文档内容
│   ├── index.en.mdx      # 英文首页
│   ├── index.zh.mdx      # 中文首页
│   └── ...
├── src/
│   ├── app/[locale]/docs/ # 国际化文档路由
│   ├── lib/source.ts     # 文档源配置
│   └── mdx-components.tsx # MDX 组件配置
├── source.config.ts      # Fumadocs 配置
└── .source/              # 自动生成的类型文件
```

### 🛠 如何启动

1. **安装依赖**:
   ```bash
   pnpm install
   ```

2. **生成文档类型**:
   ```bash
   pnpm fumadocs-mdx
   ```

3. **启动开发服务器**:
   ```bash
   pnpm dev
   ```

4. **访问文档**: 
   - 英文版: [http://localhost:3001/en/docs](http://localhost:3001/en/docs)
   - 中文版: [http://localhost:3001/zh/docs](http://localhost:3001/zh/docs)

### 🌐 语言支持

本文档系统支持多种语言：

- **English** (en) - 默认语言
- **中文** (zh) - 中文语言

您可以使用导航栏中的语言选择器在不同语言之间切换。

### 📝 添加新文档

1. 创建带有语言后缀的新 `.mdx` 文件 (例如: `page.en.mdx`, `page.zh.mdx`)
2. 添加 frontmatter:
   ```yaml
   ---
   title: 页面标题
   description: 页面描述
   ---
   ```
3. 编写内容
4. 更新对应的 `meta.json` 文件以包含新页面
5. 运行 `pnpm fumadocs-mdx` 重新生成类型

### 🔧 配置说明

- **source.config.ts**: 主要配置文件，定义文档集合和 MDX 选项
- **mdx-components.tsx**: 自定义 MDX 组件
- **src/lib/source.ts**: 支持国际化的文档源加载器配置
- **src/lib/i18n.ts**: 国际化配置

### 📚 快速导航

- [开始使用](/zh/docs/getting-started) - 详细的入门指南
- [组件库](/zh/docs/components) - 可用的 UI 组件
- [配置指南](/zh/docs/configuration) - 系统配置说明
