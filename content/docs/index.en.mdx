---
title: Documentation System
description: Modern documentation system based on Fumadocs
---

## Welcome to Documentation System

This is a modern documentation system built with **Fumadocs**, integrated with Next.js 15, React 19, and TypeScript.

### 🚀 Key Features

- **Modern UI**: Beautiful, responsive design built with Fumadocs UI
- **MDX Support**: Use React components in Markdown
- **Code Highlighting**: Syntax highlighting for multiple programming languages
- **Search Functionality**: Built-in search feature
- **Dark Mode**: Automatic dark/light mode support
- **Type Safety**: Complete TypeScript support
- **Internationalization**: Support for multiple languages with language switcher

### 📁 Project Structure

```
├── content/docs/          # Documentation content
│   ├── index.en.mdx      # English homepage
│   ├── index.zh.mdx      # Chinese homepage
│   └── ...
├── src/
│   ├── app/[locale]/docs/ # Internationalized documentation routes
│   ├── lib/source.ts     # Documentation source configuration
│   └── mdx-components.tsx # MDX component configuration
├── source.config.ts      # Fumadocs configuration
└── .source/              # Auto-generated type files
```

### 🛠 How to Start

1. **Install dependencies**:
   ```bash
   pnpm install
   ```

2. **Generate documentation types**:
   ```bash
   pnpm fumadocs-mdx
   ```

3. **Start development server**:
   ```bash
   pnpm dev
   ```

4. **Visit documentation**: 
   - English: [http://localhost:3001/en/docs](http://localhost:3001/en/docs)
   - Chinese: [http://localhost:3001/zh/docs](http://localhost:3001/zh/docs)

### 🌐 Language Support

This documentation system supports multiple languages:

- **English** (en) - Default language
- **中文** (zh) - Chinese language

You can switch between languages using the language selector in the navigation bar.

### 📝 Adding New Documentation

1. Create a new `.mdx` file with language suffix (e.g., `page.en.mdx`, `page.zh.mdx`)
2. Add frontmatter:
   ```yaml
   ---
   title: Page Title
   description: Page Description
   ---
   ```
3. Write content
4. Update the corresponding `meta.json` file to include the new page
5. Run `pnpm fumadocs-mdx` to regenerate types

### 🔧 Configuration Guide

- **source.config.ts**: Main configuration file, defines document collections and MDX options
- **mdx-components.tsx**: Custom MDX components
- **src/lib/source.ts**: Document source loader configuration with i18n support
- **src/lib/i18n.ts**: Internationalization configuration

### 📚 Quick Navigation

- [Getting Started](/en/docs/getting-started) - Detailed getting started guide
- [Components](/en/docs/components) - Available UI components
- [Configuration](/en/docs/configuration) - System configuration guide
