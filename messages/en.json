{"HomePage": {"badge": {"text": "Introducing our new components", "action": "Learn more"}, "title": "Build faster with beautiful components", "description": "Premium UI components built with React and Tailwind CSS. Save time and ship your next project faster with our ready-to-use components.", "actions": {"getStarted": "Get Started", "github": "GitHub"}, "image": {"alt": "UI Components Preview"}}, "Navigation": {"home": "Home", "docs": "Documentation", "components": "Components", "examples": "Examples", "pricing": "Pricing", "about": "About", "contact": "Contact", "signIn": "Sign In", "signUp": "Sign Up", "profile": "Profile", "settings": "Settings", "logout": "Logout"}, "SiteHeader": {"logo": {"title": "ShipSaaS.net", "alt": "blocks for shadcn/ui"}, "menu": {"home": "Home", "products": "Products", "resources": "Resources", "pricing": "Pricing", "blog": "Blog", "dashboard": "Dashboard", "company": "Company", "careers": "Careers", "support": "Support", "helpCenter": "Help Center", "contactUs": "Contact Us", "status": "Status", "termsOfService": "Terms of Service"}, "menuDescriptions": {"blog": "The latest industry news, updates, and info", "company": "Our mission is to innovate and empower the world", "careers": "Browse job listing and discover our workspace", "support": "Get in touch with our support team or visit our community forums", "helpCenter": "Get all the answers you need right here", "contactUs": "We are here to help you with any questions you have", "status": "Check the current status of our services and APIs", "termsOfService": "Our terms and conditions for using our services"}, "mobileExtraLinks": {"press": "Press", "contact": "Contact", "imprint": "Imprint", "sitemap": "Sitemap"}, "auth": {"login": "Log in", "signup": "Sign up"}}, "Common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "language": "Language", "theme": "Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode", "systemMode": "System Mode"}, "Dashboard": {"title": "Dashboard", "subtitle": "Overview and analytics", "welcome": "Welcome back", "navigation": {"dashboard": "Dashboard", "profile": "Profile", "billing": "Billing", "security": "Security", "notifications": "Notifications", "settings": "Settings"}, "profile": {"title": "Profile", "subtitle": "Manage your account information", "avatar": {"title": "Avatar", "description": "Click upload button to upload a custom one", "upload": "Upload Avatar", "recommendation": "An avatar is optional but strongly recommended"}, "name": {"title": "Name", "description": "Please enter your display name", "placeholder": "Enter your name", "validation": "Please use 3-30 characters for your name"}, "account": {"title": "Account Information", "description": "Your account details and settings", "email": "Email Address", "emailNote": "Email address cannot be changed", "userId": "User ID", "userIdNote": "Your unique user identifier"}}, "billing": {"title": "Billing", "subtitle": "Manage billing and subscriptions", "currentPlan": "Current Plan", "paymentMethod": "Payment Method", "billingHistory": "Billing History", "usageAlerts": "<PERSON><PERSON>"}, "security": {"title": "Security", "subtitle": "Security settings and authentication", "password": "Password & Authentication", "settings": "Security Settings", "sessions": "Active Sessions", "recommendations": "Security Recommendations"}, "notifications": {"title": "Notifications", "subtitle": "Notification preferences", "email": "Email Notifications", "push": "Push Notifications", "categories": "Notification Categories"}, "settings": {"title": "Settings", "subtitle": "General application settings", "appearance": "Appearance", "language": "Language & Region", "performance": "Performance", "privacy": "Data & Privacy"}}, "Footer": {"description": "Build your next project with our premium components.", "links": {"product": "Product", "company": "Company", "resources": "Resources", "legal": "Legal"}, "copyright": "© 2025 Next.js Template. All rights reserved."}}